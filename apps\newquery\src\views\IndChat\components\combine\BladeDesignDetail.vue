<!-- 叶片设计详情组件 -->
<template>
    <div
        class="mx-auto text-[#343A3F] text-sm h-full flex flex-col justify-between w-full overflow-y-auto overflow-scrollbar-hidden"
    >
        <template v-if="isLoadingDetail">
            <div class="flex items-center w-full h-full justify-center">
                <el-icon class="animate-spin mr-2"><Loading /></el-icon>
                加载中...
            </div>
        </template>
        <template v-else-if="loadError">
            <div
                class="flex items-center w-full h-full justify-center text-red-500"
            >
                加载失败
            </div>
        </template>
        <template v-else>
            <!-- 数据包基本信息 -->
            <div
                class="pt-3 pb-2 border-0 border-solid border-b border-[#e5e5e5] flex-shrink-0"
            >
                <div class="flex justify-between items-center">
                    <div class="text-[15px] text-[#121619] w-[80%] truncate">
                        数据包 {{ dataPackage?.number || '' }}
                    </div>
                    <div class="flex items-center flex-shrink-0">
                        <el-button
                            class="!text-xs flex items-center !pl-2 !pr-[10px] !h-[22px] rounded-md !bg-[#FFD1D1] !text-[#CD1525] !border-0 ml-2"
                            @click="handleDeleteDataPackage(dataPackage?.id)"
                        >
                            <img
                                src="@/assets/images/qlj/delete.png"
                                alt=""
                                class="w-4 h-4"
                            />
                            删除
                        </el-button>
                        <template v-if="dataPackage?.file_zip">
                            <a
                                :href="dataPackage.file_zip"
                                :download="`${dataPackage.number}.zip`"
                                class="text-xs flex items-center pl-2 pr-[10px] h-[22px] rounded-md bg-[#CFEAFE] text-[#129BFE] cursor-pointer ml-2"
                            >
                                <img
                                    src="@/assets/images/qlj/download.png"
                                    alt=""
                                    class="w-4"
                                />
                                <span class="relative -top-[1px]">下载</span>
                            </a>
                        </template>
                        <template v-else>
                            <el-button
                                class="!text-xs flex items-center !pl-2 !pr-[10px] !h-[22px] rounded-md !bg-[#CFEAFE] !text-[#129BFE] !border-0 ml-2"
                                @click="
                                    handleDownloadDataPackage(
                                        dataPackage?.id,
                                        dataPackage?.number
                                    )
                                "
                            >
                                <img
                                    src="@/assets/images/qlj/download.png"
                                    alt=""
                                    class="w-4"
                                />
                                下载
                            </el-button>
                        </template>
                    </div>
                </div>
            </div>
            <!-- 数据包叶片信息 -->
            <div class="flex-1 flex flex-col">
                <div class="p-3 flex flex-col flex-1">
                    <template v-if="dataPackage?.blade_sets?.length > 0">
                        <!-- 部套tab切换 -->
                        <ChangeBlade
                            v-model="selectedBlade"
                            :blade-sets="dataPackage.blade_sets"
                            class="flex-shrink-0"
                        />
                        <!-- 叶片信息 -->
                        <BladeStageDetail
                            :blades-stage-list="bladesStageList"
                            :editable="false"
                            :stage-index="selectedStageIndex"
                            :selected-blade-data-nav="selectedBladeDataNav"
                            :selected-compose-nav="selectedComposeNav"
                        />
                        <!-- @handle-stage-change="handleStageChange" -->
                    </template>
                </div>
            </div>
            <v2ConfirmsModal
                :show="showModal"
                :title="modalTitle"
                :message="modalMessage"
                @close="handleClose"
                @confirm="handleConfirm"
            />
        </template>
    </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, onMounted, type PropType } from 'vue';
import { ElIcon, ElMessage, ElLoading } from 'element-plus';
import { Loading } from '@element-plus/icons-vue';
import ChangeBlade from './bladeDesign/ChangeBlade.vue';
import BladeStageDetail from './bladeDesign/BladeStageDetail.vue';
import v2ConfirmsModal from '@/components/confirms/v2Confirms.vue';
import {
    fetchGetDataPackageDetail,
    fetchDeleteDataPackage,
    fetchDownloadDataPackage,
} from '@/services/turbine-api';
import { dataPackage as _datapackage } from '../../mock/index';

interface ModelData {
    // 数据包id
    dataPackageId: string | number;
    //选中的部套类型
    selectedBladeType?: string;
    //选中的叶片极号
    selectedBladeStage?: string | number;
    //选中的叶片导航，基本信息、构成信息、核验信息
    selectedBladeDataNav?: string;
    // 叶片构成选中导航
    selectedComposeNav?: string;
}

const props = defineProps({
    data: {
        type: Object as PropType<ModelData>,
        required: true,
    },
});

const selectedBlade = ref(props.data.selectedBladeType || ''); // 选中的部套
const bladesData = ref({}); //指定type的部套信息
const bladesStageList = ref([]); // 叶片级号列表
const selectedStageIndex = ref(0); // 设置初始值为0
const selectedBladeDataNav = ref(props.data.selectedBladeDataNav || 'basic');
const selectedComposeNav = ref(props.data.selectedComposeNav || 'shroud');
const dataPackage = ref({
    blade_sets: [],
    number: '',
    id: null,
    file_zip: '',
});
const loadError = ref(false); //请求数据包详情时，是否出错
const isLoadingDetail = ref(true); // 是否正在加载数据包详情
const elLoading = ref(null);

// // 监听子组件选中叶片级号变化，修改父组件中的级号
// const handleStageChange = index => {
//   selectedStageIndex.value = index;
// };

onMounted(async () => {
    await handleGetDataPackageDetail();
    console.log('dataPackage', dataPackage.value);
    // 设置选中布套对应的叶片信息
    if (!props.data.selectedBladeType) {
        // 没有接收到传递过来的selectedBladeType时，默认选中第一个部套
        selectedBlade.value = dataPackage.value.blade_sets[0].type;
    }
    bladesData.value = dataPackage.value.blade_sets.find(
        (item) => item.type == selectedBlade.value
    );
    bladesStageList.value = bladesData.value?.blades || [];
    // 更新selectedStageIndex
    if (props.data.selectedBladeStage) {
        const index = bladesStageList.value.findIndex(
            (item) => item.stage === props.data.selectedBladeStage
        );
        selectedStageIndex.value = index >= 0 ? index : 0;
    }
});

// 监听 selectedBlade 的变化
watch(selectedBlade, (newValue) => {
    bladesData.value = dataPackage.value.blade_sets.find(
        (item) => item.type === newValue
    );
    bladesStageList.value = bladesData.value?.blades || [];
    // 更新selectedStageIndex
    selectedStageIndex.value = 0;
    selectedBladeDataNav.value = 'basic';
    selectedComposeNav.value = 'shroud';
});

// 获取数据包详情
const handleGetDataPackageDetail = async () => {
    try {
        console.log('packageId', props.data.dataPackageId);
        const res = await fetchGetDataPackageDetail(props.data.dataPackageId);
        console.log(res), '=========获取数据包详情=========';
        // if (res.data.msg) {
        //   loadError.value = true;
        //   ElMessage.error(res.data.msg);
        // } else {
        //   dataPackage.value = res.data;
        //   console.log(res.data), '=========获取数据包详情=========';
        // }
        if (res.data.code == '0') {
            dataPackage.value = res.data.data;
            // dataPackage.value = _datapackage;
        } else if (res.data.msg) {
            loadError.value = true;
            ElMessage.error(res.data.msg);
        } else {
            loadError.value = true;
            ElMessage.error('获取数据包详情失败');
        }
    } catch (error) {
        console.log(error);
        loadError.value = true;
    } finally {
        isLoadingDetail.value = false;
    }
};

// 删除数据包
const modalTitle = ref('确定要删除此数据包吗？');
const modalMessage = ref(
    '删除时，存储在TC系统中的数据包和解析出来的全部文件将会被同步删除'
);
const showModal = ref(false);

const handleDeleteDataPackage = (id) => {
    showModal.value = true;
};

const handleClose = () => {
    showModal.value = false;
};

// 确认删除
const handleConfirm = async () => {
    try {
        showModal.value = false;

        const response = await fetchDeleteDataPackage(props.data.dataPackageId);
        if (response.data.code == '0') {
            ElMessage({
                message: '删除成功',
                type: 'success',
                duration: 2000, // 消息提示框显示2秒
                onClose: () => {
                    location.reload(); // 消息提示框关闭后刷新页面
                },
            });
        } else {
            if (response.data.msg) {
                ElMessage.error(`删除失败，` + response.data.msg);
            } else {
                ElMessage.error('删除失败');
            }
        }
    } catch (error) {
        console.error('操作失败:', error);
        ElMessage.error('操作失败，请重试');
    }
};

// 下载数据包 文件流格式
const handleDownloadDataPackage = async (id, number) => {
    try {
        elLoading.value = ElLoading.service({
            lock: true,
            text: '加载中',
            background: 'rgba(0, 0, 0, 0.5)',
        });

        // 调用fetchDownloadDataPackage获取文件流
        const blob = await fetchDownloadDataPackage(id);

        // 创建一个指向Blob对象的URL
        const downloadUrl = window.URL.createObjectURL(blob);

        // 创建临时a标签并触发下载
        const link = document.createElement('a');

        link.href = downloadUrl;
        if (number) {
            link.download = '数据包' + number + '.zip'; // 使用服务器返回的文件名
        } else {
            link.download = '数据包.zip'; // 使用服务器返回的文件名
        }
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    } catch (error) {
        console.error('下载失败:', error);
        ElMessage.error('下载失败，请重试');
    } finally {
        elLoading.value.close();
    }
};
</script>

<style scoped>
:deep(.el-input__wrapper:hover),
:deep(.el-input.is-disabled .el-input__wrapper) {
    box-shadow: none;
}
:deep(.el-input.is-disabled .el-input__wrapper),
:deep(.el-input.is-disabled .el-input__inner) {
    cursor: default !important;
}
:deep(.el-input__wrapper) {
    background: transparent;
    box-shadow: none;
    border-radius: 8px;
    overflow: hidden;
    display: inline-block;
}
:deep(.el-tabs__item.is-active) {
    color: #129bfe;
}
:deep(.el-tabs__nav-wrap::after) {
    display: none;
}
:deep(.el-tabs__nav) {
    border: none;
}
:deep(.el-tabs__active-bar) {
    background-color: #129bfe;
}
:deep(.el-tabs__item) {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: auto;
    padding: 0 20px;
}
:deep(.el-tabs__header) {
    margin: 0;
}
</style>
