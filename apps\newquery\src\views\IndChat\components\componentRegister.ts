import { defineAsyncComponent } from 'vue';

// 组件缓存
const componentCache: Record<string, any> = {};
// 组件加载状态
const loadingStates: Record<string, boolean> = {};
// 加载失败的组件记录
const failedComponents: Set<string> = new Set();

const componentMap: Record<string, () => Promise<any>> = {
    'project-collection-component': () =>
        import('./combine/CenterProjectCollectionComponent.vue'),
    'opportunity-list-component': () =>
        import('./combine/CenterOpportunityListComponent.vue'),
    'project-list-component': () =>
        import('./combine/CenterProjectListComponent.vue'),
    'business-amount-component': () =>
        import('./combine/CenterBusinessAmountComponent.vue'),
    'recommend-system-link-component': () =>
        import('./combine/RecommendSystemLinkComponent.vue'),
    'list-component': () => import('./combine/ListComponent.vue'),
    'total-summary-component': () =>
        import('./combine/TotalSummaryComponent.vue'),
    'text-detail-component': () => import('./combine/TextDetailComponent.vue'),
    'workbench-text-detail-component': () =>
        import('./combine/WorkbenchTextDetailComponent.vue'),
    'pdf-viewer-component': () => import('./combine/PDFViewerComponent.vue'),
    // "file-list-component": () => import("./combine/FileListComponent.vue"),
    'file-list-component': () =>
        import('./combine/FileListAndPagesComponent.vue'),
    'upload-data-package-component': () =>
        import('./combine/BladeDesignAddComponent.vue'), // 上传数据包组件
    'default-page-component': () =>
        import('./combine/DefaultPageComponent.vue'), // 缺省页组件
    'blade-design-detail-component': () =>
        import('./combine/BladeDesignDetail.vue'), // 叶片设计详情（查询）组件
    'blade-structure-detail-component': () =>
        import('./combine/BladeStructureDetail.vue'), // 叶片结构详情组件

    'failed-blade-component': () =>
        import('./combine/FailedBladeComponent.vue'), // 叶片结构解析中，解析失败的组件

    // Model3DComponent
    'model-3d-component': () => import('./combine/Model3DComponent.vue'),
    'prediction-result-component': () =>
        import('./combine/PredictionResultComponent.vue').then(
            (m) => m.default
        ),

    // QualityInspectionReportComponent.vue
    'quality-inspection-report-component': () =>
        import('./combine/QualityInspectionReportComponent.vue').then(
            (m) => m.default
        ),

    // VideoDisplayComponent.vue
    'video-display-component': () =>
        import('./combine/VideoDisplayComponent.vue').then((m) => m.default),

    // BladeProcessComponent.vue
    'blade-process-component': () =>
        import('./combine/BladeProcessComponent.vue').then((m) => m.default),
};

// 基础加载组件
const LoadingComponent = {
    template: `
    <div class="flex items-center justify-center h-full">
      <div class="text-gray-500">加载中...</div>
    </div>
  `,
};

// 基础错误组件
const ErrorComponent = {
    template: `
    <div class="flex items-center justify-center h-full">
      <div class="text-red-500">加载失败</div>
    </div>
  `,
};

// 注册新组件
export function registerComponent(uid: string, loader: () => Promise<any>) {
    componentMap[uid] = loader;
}

// 增强的组件加载器，带重试和指数退避
const createEnhancedLoader = (
    originalLoader: () => Promise<any>,
    uid: string
) => {
    return async () => {
        let lastError: any;

        // 最多重试5次，使用指数退避
        for (let attempt = 0; attempt < 5; attempt++) {
            try {
                // 防止并发加载同一组件
                if (loadingStates[uid]) {
                    // 等待其他实例加载完成
                    let waitCount = 0;
                    while (loadingStates[uid] && waitCount < 50) {
                        await new Promise((resolve) =>
                            setTimeout(resolve, 100)
                        );
                        waitCount++;
                    }

                    // 如果已经加载成功，直接返回缓存的结果
                    if (componentCache[uid]) {
                        return componentCache[uid];
                    }
                }

                loadingStates[uid] = true;

                console.log(`正在加载组件: ${uid}, 尝试次数: ${attempt + 1}`);

                const result = await originalLoader();

                // 验证加载的结果
                if (!result) {
                    throw new Error(`组件 ${uid} 加载结果为空`);
                }

                // 检查是否是有效的Vue组件
                const component = result.default || result;
                if (
                    !component ||
                    (typeof component !== 'object' &&
                        typeof component !== 'function')
                ) {
                    throw new Error(
                        `组件 ${uid} 加载结果无效: 不是有效的Vue组件`
                    );
                }

                // 缓存成功加载的组件
                componentCache[uid] = result;
                failedComponents.delete(uid);

                console.log(`组件加载成功: ${uid}`, {
                    hasDefault: !!result.default,
                    type: typeof component,
                });
                return result;
            } catch (error) {
                lastError = error;
                console.warn(
                    `组件加载失败: ${uid}, 尝试次数: ${attempt + 1}`,
                    error
                );

                // 指数退避：100ms, 200ms, 400ms, 800ms, 1600ms
                if (attempt < 4) {
                    const delay = Math.pow(2, attempt) * 100;
                    await new Promise((resolve) => setTimeout(resolve, delay));
                }
            } finally {
                loadingStates[uid] = false;
            }
        }

        // 所有重试都失败了
        failedComponents.add(uid);
        console.error(`组件加载彻底失败: ${uid}`, lastError);
        throw lastError;
    };
};

// 清理失败的组件，允许重新尝试加载
export function retryFailedComponent(uid: string) {
    failedComponents.delete(uid);
    delete componentCache[uid];
    loadingStates[uid] = false;
}

// 预加载核心组件
export function preloadCoreComponents() {
    const coreComponents = [
        'file-list-component',
        'text-detail-component',
        'list-component',
        'pdf-viewer-component',
    ];

    coreComponents.forEach((uid) => {
        if (!componentCache[uid] && !loadingStates[uid]) {
            const loader = componentMap[uid];
            if (loader) {
                createEnhancedLoader(loader, uid)()
                    .then(() => console.log(`核心组件预加载成功: ${uid}`))
                    .catch(() => console.warn(`核心组件预加载失败: ${uid}`));
            }
        }
    });
}

// 获取组件加载状态统计
export function getComponentStats() {
    return {
        cached: Object.keys(componentCache).length,
        loading: Object.keys(loadingStates).filter((uid) => loadingStates[uid])
            .length,
        failed: failedComponents.size,
        total: Object.keys(componentMap).length,
        failedList: Array.from(failedComponents),
    };
}

// 批量重试失败的组件
export function retryAllFailedComponents() {
    const failed = Array.from(failedComponents);
    failed.forEach((uid) => {
        retryFailedComponent(uid);
    });
    return failed;
}

// 调试函数：获取详细的组件状态
export function debugComponentStatus(uid?: string) {
    if (uid) {
        const loader = componentMap[uid];
        return {
            uid,
            exists: !!loader,
            cached: !!componentCache[uid],
            loading: !!loadingStates[uid],
            failed: failedComponents.has(uid),
            cacheContent: componentCache[uid] ? 'exists' : 'empty',
        };
    }

    // 返回所有组件的状态
    const allComponents = Object.keys(componentMap);
    return allComponents.map((uid) => ({
        uid,
        cached: !!componentCache[uid],
        loading: !!loadingStates[uid],
        failed: failedComponents.has(uid),
    }));
}

// 清理所有缓存（用于调试）
export function clearAllCache() {
    Object.keys(componentCache).forEach((uid) => {
        delete componentCache[uid];
    });
    Object.keys(loadingStates).forEach((uid) => {
        loadingStates[uid] = false;
    });
    failedComponents.clear();
    console.log('所有组件缓存已清理');
}

// 在开发环境下暴露调试函数到全局
if (import.meta.env.DEV) {
    (window as any).__componentDebug = {
        getStats: getComponentStats,
        debugStatus: debugComponentStatus,
        clearCache: clearAllCache,
        retryFailed: retryAllFailedComponents,
        retryComponent: retryFailedComponent,
        preloadCore: preloadCoreComponents,
    };
    console.log('组件调试工具已挂载到 window.__componentDebug');
}

// 解析组件
export function resolveComponent(uid: string) {
    const loader = componentMap[uid];
    if (!loader) {
        return defineAsyncComponent({
            loader: () => import('./combine/NotFound.vue'),
            loadingComponent: LoadingComponent,
            errorComponent: ErrorComponent,
        });
    }

    return defineAsyncComponent({
        loader: createEnhancedLoader(loader, uid),
        loadingComponent: LoadingComponent,
        errorComponent: {
            template: `
        <div class="flex flex-col items-center justify-center h-full space-y-2">
          <div class="text-red-500">组件加载失败</div>
          <button
            @click="retry"
            class="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            重新加载
          </button>
        </div>
      `,
            methods: {
                retry() {
                    retryFailedComponent(uid);
                    // 触发重新加载
                    this.$parent?.$forceUpdate?.();
                },
            },
        },
        delay: 200,
        timeout: 15000, // 增加超时时间
        suspensible: false,
        onError(error, retry, fail, attempts) {
            console.error(`组件 ${uid} 加载错误，尝试次数: ${attempts}`, error);

            // 对于网络错误，给更多重试机会
            if (
                error.name === 'ChunkLoadError' ||
                error.message.includes('Loading chunk')
            ) {
                if (attempts <= 2) {
                    console.log(`检测到chunk加载错误，将重试加载组件: ${uid}`);
                    // 短暂延迟后重试
                    setTimeout(() => retry(), 1000);
                } else {
                    fail();
                }
            } else {
                fail();
            }
        },
    });
}
