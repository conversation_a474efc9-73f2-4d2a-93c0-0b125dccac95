/* eslint-disable no-unused-vars */
import { markRaw } from 'vue';
import { resolveComponent } from '../components/componentRegister';
import { PreviewMessage, MessageComponent } from './types';

export function usePreviewComponents() {
    const generateComponentKey = (uid: string): string => {
        return `${uid}-${Date.now()}`;
    };

    const createComponent = (uid: string, data: any): MessageComponent => {
        try {
            const component = resolveComponent(uid);
            console.log(`创建组件: ${uid}`, {
                component,
                hasComponent: !!component,
            });

            return {
                component: markRaw(component),
                data: data,
                key: generateComponentKey(uid),
            };
        } catch (error) {
            console.error(`创建组件失败: ${uid}`, error);
            // 返回一个错误组件
            return {
                component: markRaw({
                    template: `<div class="text-red-500 p-4">组件 ${uid} 加载失败</div>`,
                }),
                data: data,
                key: generateComponentKey(uid),
            };
        }
    };

    const updatePreviewComponents = (
        messages: PreviewMessage | PreviewMessage[]
    ): PreviewMessage[] => {
        if (!Array.isArray(messages)) {
            messages = [messages];
        }

        return messages.map((msg) => {
            if (!msg?.uid || !msg?.area) return msg;

            const componentInfo = createComponent(msg.uid, msg.data);
            return {
                ...msg,
                componentKey: componentInfo.key,
                component: componentInfo.component,
                data: componentInfo.data,
            };
        });
    };

    return {
        updatePreviewComponents,
    };
}
