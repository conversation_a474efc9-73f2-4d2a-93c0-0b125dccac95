<!-- 叶片结构详情组件 -->
<template>
    <div class="mx-auto text-[#343A3F] text-sm h-full w-full">
        <template v-if="isLoadingDetail">
            <div class="flex items-center w-full h-full justify-center">
                <el-icon class="animate-spin mr-2"><Loading /></el-icon>
                加载中...
            </div>
        </template>
        <template v-else-if="loadError">
            <div
                class="flex items-center w-full h-full justify-center text-red-500"
            >
                加载失败
            </div>
        </template>
        <template v-else>
            <div
                class="container-center-div overflow-y-auto flex flex-col"
                v-if="selectedTab == '3d' || selectedTab == '2d'"
            >
                <!-- 3D/2D模型信息 -->
                <div
                    class="border-0 border-solid border-b border-[#e5e5e5] py-2 mb-3 text-[15px] font-medium flex items-center justify-center"
                >
                    {{ selectedBladeStageName }} <span class="mx-1">|</span
                    >{{ selectedBladeStagePartNumber }}
                </div>
                <template v-if="isLoading">
                    <div class="flex items-center w-full h-full justify-center">
                        <el-icon class="animate-spin mr-2"><Loading /></el-icon>
                        加载中...
                    </div>
                </template>
                <template v-else>
                    <Model3DComponent
                        v-if="selectedTab == '3d' && !isLoading && data3D"
                        :data="data3D"
                        containerClass="flex-1"
                    />
                    <PDFViewerComponent
                        v-if="selectedTab == '2d' && !isLoading && data2D"
                        :data="data2D"
                        containerClass="flex-1"
                        class="flex-1"
                    />
                    <!-- 解析中或解析失败组件 -->
                    <FailedBladeComponent
                        v-if="isShowFailedBlade"
                        :title="errorTitle"
                        :description="errorDescription"
                        :error-code="errorCode"
                        containerClass="flex-1"
                        @refresh="handleRefresh"
                        :showRefresh="true"
                    />
                </template>
            </div>
            <div
                v-else
                class="container-center-div overflow-y-auto overflow-scrollbar-hidden flex flex-col"
            >
                <!-- 数据包基本信息 -->
                <div
                    class="pt-3 pb-2 border-0 border-solid border-b border-[#e5e5e5] flex-shrink-0"
                >
                    <div class="flex justify-between items-center">
                        <div
                            class="text-[15px] text-[#121619] w-[80%] truncate"
                        >
                            数据包 {{ dataPackage?.number || '' }}
                        </div>
                        <div class="flex items-center">
                            <template v-if="dataPackage?.file_zip">
                                <a
                                    :href="dataPackage.file_zip"
                                    :download="`${dataPackage.number}.zip`"
                                    class="text-xs flex items-center pl-2 pr-[10px] h-[22px] rounded-md bg-[#CFEAFE] text-[#129BFE] cursor-pointer"
                                >
                                    <img
                                        src="@/assets/images/qlj/download.png"
                                        alt=""
                                        class="w-4"
                                    />
                                    <span class="relative -top-[1px]"
                                        >下载</span
                                    >
                                </a>
                            </template>
                            <template v-else>
                                <el-button
                                    class="!text-xs flex items-center !pl-2 !pr-[10px] !h-[22px] rounded-md !bg-[#CFEAFE] !text-[#129BFE] !border-0 ml-2"
                                    @click="
                                        handleDownloadDataPackage(
                                            dataPackage?.id,
                                            dataPackage?.number
                                        )
                                    "
                                >
                                    <img
                                        src="@/assets/images/qlj/download.png"
                                        alt=""
                                        class="w-4"
                                    />
                                    下载
                                </el-button>
                            </template>
                        </div>
                    </div>
                </div>
                <!-- 数据包叶片信息 -->
                <div class="flex-1 flex flex-col">
                    <div class="p-3 flex flex-col flex-1">
                        <template v-if="dataPackage?.blade_sets?.length > 0">
                            <!-- 部套tab切换 -->
                            <ChangeBlade
                                v-model="selectedBlade"
                                :blade-sets="dataPackage.blade_sets"
                                class="flex-shrink-0"
                            />
                            <!-- 叶片信息 -->
                            <BladeStageDetail
                                :blades-stage-list="bladesStageList"
                                :editable="false"
                                :stage-index="selectedStageIndex"
                                :selected-blade-data-nav="selectedBladeDataNav"
                                :selected-compose-nav="selectedComposeNav"
                                @handle-stage-change="handleStageChange"
                            />
                        </template>
                    </div>
                </div>
            </div>
            <div
                class="flex justify-end px-6 border-0 border-solid border-t border-[#e5e5e5] flex-shrink-0 overflow-hidden h-[56px]"
            >
                <el-button
                    :class="[
                        'flex-shrink-0 !border-t-0 !m-0 !h-10',
                        selectedTab == 'design'
                            ? '!bg-[#CFEAFE] !text-[#129bfe] !border-[#129bfe]'
                            : '',
                    ]"
                    style="border-radius: 0px 0px 8px 8px"
                    @click="handleTabChange('design')"
                    >D-设计参数</el-button
                >
                <el-button
                    :class="[
                        'flex-shrink-0 !border-t-0 !m-0 !h-10',
                        selectedTab == '3d'
                            ? '!bg-[#CFEAFE] !text-[#129bfe] !border-[#129bfe]'
                            : '',
                    ]"
                    style="border-radius: 0px 0px 8px 8px"
                    @click="handleTabChange('3d')"
                    >3D-模型信息</el-button
                >
                <el-button
                    :class="[
                        'flex-shrink-0 !border-t-0 !m-0 !h-10',
                        selectedTab == '2d'
                            ? '!bg-[#CFEAFE] !text-[#129bfe] !border-[#129bfe]'
                            : '',
                    ]"
                    style="border-radius: 0px 0px 8px 8px"
                    @click="handleTabChange('2d')"
                    >2D-图纸信息</el-button
                >
            </div>
        </template>
    </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, type PropType } from 'vue';
import { ElIcon, ElMessage, ElLoading } from 'element-plus';
import { Loading } from '@element-plus/icons-vue';
import ChangeBlade from './bladeDesign/ChangeBlade.vue';
import BladeStageDetail from './bladeDesign/BladeStageDetail.vue';
import {
    fetchGetDataPackageDetail,
    fetchDownloadDataPackage,
    fetchGetBlade2D3DFile,
} from '@/services/turbine-api';
import { dataPackage as _datapackage } from '../../mock/index';
import PDFViewerComponent from './PDFViewerComponent.vue';
import FailedBladeComponent from './FailedBladeComponent.vue';
import Model3DComponent from './Model3DComponent.vue';

interface ModelData {
    // 数据包id
    dataPackageId: string | number;
    //选中的部套类型
    selectedBladeType?: string;
    //选中的叶片极号
    selectedBladeStage?: string | number;
    //选中的叶片导航，基本信息、构成信息、核验信息
    selectedBladeDataNav?: string;
    // 叶片构成选中导航
    selectedComposeNav?: string;
    // 选中的tab
    selectedTab?: string;
}

const props = defineProps({
    data: {
        type: Object as PropType<ModelData>,
        required: true,
    },
});

const selectedBlade = ref(props.data.selectedBladeType || ''); // 选中的部套
const bladesData = ref({}); //指定type的部套信息
const bladesStageList = ref([]); // 叶片级号列表
const selectedStageIndex = ref(0); // 设置初始值为0
// 选中叶片级号名称
const selectedBladeStageName = ref('');
// 选中叶片级号零件图号
const selectedBladeStagePartNumber = ref('');
// 选中叶片id
const selectedBladeStageId = ref('');
const selectedBladeDataNav = ref(props.data.selectedBladeDataNav || 'basic');
const selectedComposeNav = ref(props.data.selectedComposeNav || 'shroud');
const dataPackage = ref({
    blade_sets: [],
    number: '',
    id: null,
    file_zip: '',
    file_2d: '',
    file_3d: '',
});
const loadError = ref(false); //请求数据包详情时，是否出错
const isLoadingDetail = ref(true); // 是否正在加载数据包详情
const elLoading = ref(null);

onMounted(async () => {
    console.log('isLoadingDetail', isLoadingDetail.value);
    await handleGetDataPackageDetail();
    // console.log(dataPackage.value, '===请求完的数据');
    // 设置选中布套对应的叶片信息
    if (!props.data.selectedBladeType) {
        // 没有接收到传递过来的selectedBladeType时，默认选中第一个部套
        selectedBlade.value = dataPackage.value.blade_sets[0].type;
    }
    bladesData.value = dataPackage.value.blade_sets.find(
        (item) => item.type == selectedBlade.value
    );
    bladesStageList.value = bladesData.value?.blades || [];
    // 更新selectedStageIndex
    if (props.data.selectedBladeStage) {
        const index = bladesStageList.value.findIndex(
            (item) => item.stage === props.data.selectedBladeStage
        );
        selectedStageIndex.value = index >= 0 ? index : 0;
    }
    selectedBladeStageName.value =
        bladesStageList.value[selectedStageIndex.value].name;
    selectedBladeStagePartNumber.value =
        bladesStageList.value[selectedStageIndex.value].blade_data?.basic[0]
            .value;
    selectedBladeStageId.value =
        bladesStageList.value[selectedStageIndex.value].id;
});

// 监听 selectedBlade 的变化
watch(selectedBlade, (newValue) => {
    bladesData.value = dataPackage.value.blade_sets.find(
        (item) => item.type === newValue
    );
    bladesStageList.value = bladesData.value?.blades || [];
    // 更新selectedStageIndex
    selectedStageIndex.value = 0;
    selectedBladeDataNav.value = 'basic';
    selectedComposeNav.value = 'shroud';
    selectedBladeStageName.value =
        bladesStageList.value[selectedStageIndex.value].name;
    selectedBladeStagePartNumber.value =
        bladesStageList.value[selectedStageIndex.value].blade_data?.basic[0]
            .value;
    selectedBladeStageId.value =
        bladesStageList.value[selectedStageIndex.value].id;
});

// 获取数据包详情
const handleGetDataPackageDetail = async () => {
    try {
        const res = await fetchGetDataPackageDetail(props.data.dataPackageId);
        // if (res.data.msg) {
        //   loadError.value = true;
        //   ElMessage.error(res.data.msg);
        // } else {
        //   dataPackage.value = res.data;
        //   console.log(res.data), '=========获取数据包详情=========';
        // }
        if (res.data.code == '0') {
            dataPackage.value = res.data.data;
        } else if (res.data.msg) {
            loadError.value = true;
            ElMessage.error(res.data.msg);
        } else {
            loadError.value = true;
            // ElMessage.error('获取数据包详情失败');
        }
    } catch (error) {
        console.log(error);
        loadError.value = true;
    } finally {
        isLoadingDetail.value = false;
    }
};
// 下载数据包 文件流格式
const handleDownloadDataPackage = async (id, number) => {
    try {
        elLoading.value = ElLoading.service({
            lock: true,
            text: '加载中',
            background: 'rgba(0, 0, 0, 0.5)',
        });

        // 调用fetchDownloadDataPackage获取文件流
        const blob = await fetchDownloadDataPackage(id);

        // 创建一个指向Blob对象的URL
        const downloadUrl = window.URL.createObjectURL(blob);

        // 创建临时a标签并触发下载
        const link = document.createElement('a');

        link.href = downloadUrl;
        if (number) {
            link.download = '数据包' + number + '.zip'; // 使用服务器返回的文件名
        } else {
            link.download = '数据包.zip'; // 使用服务器返回的文件名
        }
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    } catch (error) {
        console.error('下载失败:', error);
        ElMessage.error('下载失败，请重试');
    } finally {
        elLoading.value.close();
    }
};

// 处理叶片级号变化
const handleStageChange = (index) => {
    selectedStageIndex.value = index;
    selectedBladeStageName.value =
        bladesStageList.value[selectedStageIndex.value].name;
    selectedBladeStagePartNumber.value =
        bladesStageList.value[selectedStageIndex.value].blade_data?.basic[0]
            .value;
    selectedBladeStageId.value =
        bladesStageList.value[selectedStageIndex.value].id;
};

// 处理tab切换
const selectedTab = ref(props.data.selectedTab || 'design');
const data2D = ref(null); //2d图纸信息
const isLoading = ref(false); //等数据加载完，在渲染子组件
const isShowFailedBlade = ref(false); //是否显示失败blade
const errorTitle = ref(''); //错误标题
const errorCode = ref(''); //错误码
const data3D = ref(null); //3d图纸信息
const errorDescription = ref(''); //错误描述

const handleTabChange = (tab) => {
    selectedTab.value = tab;
    if (tab == '2d') {
        // 调用接口获取2D图纸信息
        handleGet2DData();
    } else if (tab == '3d') {
        // 调用接口获取3D图纸信息
        handleGet3DData();
    } else {
        // 更新selectedStageIndex
        selectedStageIndex.value = 0;
        selectedBladeDataNav.value = 'basic';
        selectedComposeNav.value = 'shroud';
    }
};

// 重新加载
const handleRefresh = () => {
    if (selectedTab.value == '2d') {
        // 调用接口获取2D图纸信息
        handleGet2DData();
    } else if (selectedTab.value == '3d') {
        // 调用接口获取3D图纸信息
        handleGet3DData();
    }
};

// 获取2D图纸信息
const handleGet2DData = async () => {
    isShowFailedBlade.value = false;
    try {
        if (dataPackage.value?.file_2d) {
            data2D.value = {
                fileUrl: dataPackage.value.file_2d,
            };
        } else {
            isLoading.value = true;
            const res = await fetchGetBlade2D3DFile(
                selectedBladeStageId.value,
                '2d'
            );
            if (
                res.data.code == '0' &&
                res.data.data &&
                res.data.data.file_2d_exists
            ) {
                data2D.value = {
                    fileUrl: res.data.data.file_2d,
                };
            } else {
                isShowFailedBlade.value = true;
                let _errorTitle = '';
                let _errorDescription = '';
                if (res.data.code == '820201') {
                    _errorTitle = '解析中';
                    _errorDescription = '文件正在处理中，请稍候再试';
                } else if (res.data.code == '820202') {
                    _errorTitle = '解析失败';
                    _errorDescription = '文件解析错误/未知状态';
                } else if (res.data.code == '820203') {
                    _errorTitle = '文件不存在';
                    _errorDescription = '未解析出文件';
                } else {
                    _errorTitle = '解析失败';
                    _errorDescription = '请稍后重试';
                }
                errorTitle.value = _errorTitle;
                errorDescription.value = _errorDescription;
                errorCode.value = res.data.code;
            }
        }
    } catch (error) {
        isShowFailedBlade.value = true;
        errorTitle.value = '获取失败';
        errorDescription.value = '请求出错，请重试';
        console.error(error);
    } finally {
        isLoading.value = false;
    }
};

// 获取3D图纸信息
const handleGet3DData = async () => {
    isShowFailedBlade.value = false;
    console.log('获取3D图纸信息');
    try {
        if (dataPackage.value?.file_3d) {
            data3D.value = {
                modelUrl: dataPackage.value.file_3d,
            };
        } else {
            isLoading.value = true;
            const res = await fetchGetBlade2D3DFile(
                selectedBladeStageId.value,
                '3d'
            );
            if (
                res.data.code == '0' &&
                res.data.data &&
                res.data.data.file_3d_exists
            ) {
                data3D.value = {
                    modelUrl: res.data.data.file_3d,
                };
            } else {
                isShowFailedBlade.value = true;
                let _errorTitle = '';
                let _errorDescription = '';
                if (res.data.code == '820201') {
                    _errorTitle = '解析中';
                    _errorDescription = '文件正在处理中，请稍候再试';
                } else if (res.data.code == '820202') {
                    _errorTitle = '解析失败';
                    _errorDescription = '文件解析错误/未知状态';
                } else if (res.data.code == '820203') {
                    _errorTitle = '文件不存在';
                    _errorDescription = '未解析出文件';
                } else {
                    _errorTitle = '解析失败';
                    _errorDescription = '请稍后重试';
                }
                errorTitle.value = _errorTitle;
                errorDescription.value = _errorDescription;
                errorCode.value = res.data.code;
            }
        }
    } catch (error) {
        isShowFailedBlade.value = true;
        errorTitle.value = '获取失败';
        errorDescription.value = '请求出错，请重试';
        console.error(error);
    } finally {
        isLoading.value = false;
    }
};

if (props.data.selectedTab == '2d') {
    handleGet2DData();
} else if (props.data.selectedTab == '3d') {
    handleGet3DData();
}
</script>

<style scoped>
.container-center-div {
    height: calc(100% - 56px);
}
:deep(.el-input__wrapper:hover),
:deep(.el-input.is-disabled .el-input__wrapper) {
    box-shadow: none;
}
:deep(.el-input.is-disabled .el-input__wrapper),
:deep(.el-input.is-disabled .el-input__inner) {
    cursor: default !important;
}
:deep(.el-input__wrapper) {
    background: transparent;
    box-shadow: none;
    border-radius: 8px;
    overflow: hidden;
    display: inline-block;
}
:deep(.el-tabs__item.is-active) {
    color: #129bfe;
}
:deep(.el-tabs__nav-wrap::after) {
    display: none;
}
:deep(.el-tabs__nav) {
    border: none;
}
:deep(.el-tabs__active-bar) {
    background-color: #129bfe;
}
:deep(.el-tabs__item) {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: auto;
    padding: 0 20px;
}
:deep(.el-tabs__header) {
    margin: 0;
}
</style>
